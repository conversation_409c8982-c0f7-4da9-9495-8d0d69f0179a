<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>