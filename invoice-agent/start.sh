#!/bin/bash

# Invoice Agent 启动脚本

echo "🚀 启动 Invoice Agent 系统..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查是否在invoice-agent目录
if [ ! -f "requirements.txt" ]; then
    echo "❌ 请在invoice-agent目录下运行此脚本"
    exit 1
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "📥 安装Python依赖..."
pip install -r requirements.txt

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️  .env文件不存在，从.env.example复制..."
    cp .env.example .env
    echo "📝 请编辑.env文件，设置OPENAI_API_KEY等必要配置"
    echo "   然后重新运行此脚本"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要目录..."
mkdir -p uploads
mkdir -p static
mkdir -p logs

# 检查Docker（用于n8n）
if ! command -v docker &> /dev/null; then
    echo "⚠️  Docker未安装，将跳过n8n启动"
    echo "   如需使用n8n工作流，请安装Docker"
    START_N8N=false
else
    START_N8N=true
fi

# 启动API服务
echo "🌐 启动Invoice Agent API服务..."
cd api
python main.py &
API_PID=$!
cd ..

echo "✅ API服务已启动 (PID: $API_PID)"
echo "   访问地址: http://localhost:8000"

# 启动n8n（如果Docker可用）
if [ "$START_N8N" = true ]; then
    echo "🔄 启动n8n工作流引擎..."
    cd n8n
    docker-compose up -d
    cd ..
    
    echo "✅ n8n已启动"
    echo "   访问地址: http://localhost:5678"
    echo "   用户名: admin"
    echo "   密码: admin123"
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
echo "🔍 检查服务状态..."

# 检查API服务
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ API服务运行正常"
else
    echo "❌ API服务启动失败"
fi

# 检查n8n服务
if [ "$START_N8N" = true ]; then
    if curl -s http://localhost:5678 > /dev/null; then
        echo "✅ n8n服务运行正常"
    else
        echo "❌ n8n服务启动失败"
    fi
fi

echo ""
echo "🎉 Invoice Agent 系统启动完成!"
echo ""
echo "📋 服务信息:"
echo "   - API服务: http://localhost:8000"
echo "   - 上传表单: http://localhost:8000"
echo "   - API文档: http://localhost:8000/docs"
if [ "$START_N8N" = true ]; then
    echo "   - n8n工作流: http://localhost:5678"
    echo "   - n8n webhook: http://localhost:5678/webhook/invoice-upload"
fi
echo ""
echo "🧪 运行测试:"
echo "   python test_api.py"
echo ""
echo "🛑 停止服务:"
echo "   ./stop.sh"
echo ""

# 保存PID以便停止
echo $API_PID > .api_pid

echo "按Ctrl+C停止服务..."
wait $API_PID
