{"name": "Invoice Processing Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "invoice-upload", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "invoice-upload"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.file_url}}", "operation": "isNotEmpty"}]}}, "id": "check-file-input", "name": "Check File Input", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "={{$json.file_url}}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "id": "download-file", "name": "Download File", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [680, 200]}, {"parameters": {"jsCode": "// 处理文件数据，转换为base64\nconst fileData = $input.first().binary.data;\nconst fileName = $input.first().json.file_name || 'uploaded_file';\nconst fileExtension = fileName.split('.').pop().toLowerCase();\n\n// 将文件数据转换为base64\nconst base64Data = fileData.toString('base64');\n\nreturn {\n  file_data: base64Data,\n  file_extension: fileExtension,\n  file_name: fileName,\n  customer_name: $json.customer_name || null,\n  use_llm: $json.use_llm !== false\n};"}, "id": "process-file-data", "name": "Process File Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"jsCode": "// 直接使用上传的文件数据\nreturn {\n  file_data: $json.file_data,\n  file_extension: $json.file_extension || 'jpg',\n  file_name: $json.file_name || 'direct_upload',\n  customer_name: $json.customer_name || null,\n  use_llm: $json.use_llm !== false\n};"}, "id": "use-direct-data", "name": "Use Direct Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 400]}, {"parameters": {}, "id": "merge-data", "name": "Merge Data", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"url": "http://localhost:8000/webhook/n8n", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "file_data", "value": "={{$json.file_data}}"}, {"name": "file_extension", "value": "={{$json.file_extension}}"}, {"name": "customer_name", "value": "={{$json.customer_name}}"}, {"name": "use_llm", "value": "={{$json.use_llm}}"}]}, "options": {}}, "id": "call-invoice-api", "name": "Call Invoice API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1340, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.error}}", "operation": "isEmpty"}]}}, "id": "check-processing-result", "name": "Check Processing Result", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"jsCode": "// 处理成功的结果\nconst result = $json;\n\n// 格式化输出\nconst formattedResult = {\n  success: true,\n  customer_name: result.customer_name,\n  order_date: result.order_date,\n  total_items: result.summary?.total_items || 0,\n  matched_items: result.summary?.matched_items || 0,\n  total_amount: result.summary?.total_amount || 0,\n  status: result.status,\n  items: result.items || [],\n  requires_review: result.processing_info?.requires_manual_review || false,\n  processed_at: new Date().toISOString()\n};\n\nreturn formattedResult;"}, "id": "format-success-result", "name": "Format Success Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 200]}, {"parameters": {"jsCode": "// 处理错误结果\nconst result = $json;\n\nconst errorResult = {\n  success: false,\n  error: result.error || 'Unknown error occurred',\n  processed_at: new Date().toISOString()\n};\n\nreturn errorResult;"}, "id": "format-error-result", "name": "Format Error Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 400]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.requires_review}}", "value2": true}]}}, "id": "check-review-needed", "name": "Check Review Needed", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2000, 200]}, {"parameters": {"subject": "订单需要人工审核 - {{$json.customer_name}}", "message": "订单处理完成，但需要人工审核：\n\n客户：{{$json.customer_name}}\n订单日期：{{$json.order_date}}\n总金额：¥{{$json.total_amount}}\n状态：{{$json.status}}\n\n请登录系统查看详细信息。", "options": {}}, "id": "send-review-notification", "name": "Send Review Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [2220, 100]}, {"parameters": {"respondWith": "json", "responseBody": "={{$json}}"}, "id": "return-final-result", "name": "Return Final Result", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2220, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Check File Input", "type": "main", "index": 0}]]}, "Check File Input": {"main": [[{"node": "Download File", "type": "main", "index": 0}], [{"node": "Use Direct Data", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Process File Data", "type": "main", "index": 0}]]}, "Process File Data": {"main": [[{"node": "Merge Data", "type": "main", "index": 0}]]}, "Use Direct Data": {"main": [[{"node": "Merge Data", "type": "main", "index": 1}]]}, "Merge Data": {"main": [[{"node": "Call Invoice API", "type": "main", "index": 0}]]}, "Call Invoice API": {"main": [[{"node": "Check Processing Result", "type": "main", "index": 0}]]}, "Check Processing Result": {"main": [[{"node": "Format Success Result", "type": "main", "index": 0}], [{"node": "Format Error Result", "type": "main", "index": 0}]]}, "Format Success Result": {"main": [[{"node": "Check Review Needed", "type": "main", "index": 0}]]}, "Format Error Result": {"main": [[{"node": "Return Final Result", "type": "main", "index": 0}]]}, "Check Review Needed": {"main": [[{"node": "Send Review Notification", "type": "main", "index": 0}], [{"node": "Return Final Result", "type": "main", "index": 0}]]}, "Send Review Notification": {"main": [[{"node": "Return Final Result", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1"}