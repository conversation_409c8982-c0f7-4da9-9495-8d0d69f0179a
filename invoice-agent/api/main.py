from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
import os
import logging
import json
from datetime import datetime
from typing import Optional
import aiofiles

# 导入服务
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from services.ocr_service import ocr_service
from services.llm_service import llm_service
from services.product_service import product_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Invoice Agent API", version="1.0.0")

# 创建必要的目录
os.makedirs("uploads", exist_ok=True)
os.makedirs("static", exist_ok=True)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def upload_form():
    """显示文件上传表单"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Invoice Agent - 订单处理系统</title>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .container { background: #f5f5f5; padding: 30px; border-radius: 10px; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            button { background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .result { margin-top: 30px; padding: 20px; background: white; border-radius: 5px; }
            .error { color: red; }
            .success { color: green; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Invoice Agent - 订单处理系统</h1>
            <p>上传订单文件（支持图片和PDF），系统将自动提取订单信息并匹配产品。</p>
            
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="file">选择文件:</label>
                    <input type="file" id="file" name="file" accept=".jpg,.jpeg,.png,.pdf" required>
                </div>
                
                <div class="form-group">
                    <label for="customer_name">客户姓名 (可选):</label>
                    <input type="text" id="customer_name" name="customer_name" placeholder="如果文件中没有，请手动输入">
                </div>
                
                <div class="form-group">
                    <label for="use_llm">使用LLM增强匹配:</label>
                    <select id="use_llm" name="use_llm">
                        <option value="true">是</option>
                        <option value="false">否</option>
                    </select>
                </div>
                
                <button type="submit">处理订单</button>
            </form>
            
            <div id="result" class="result" style="display: none;"></div>
        </div>

        <script>
            document.getElementById('uploadForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData();
                const fileInput = document.getElementById('file');
                const customerName = document.getElementById('customer_name').value;
                const useLlm = document.getElementById('use_llm').value;
                
                if (!fileInput.files[0]) {
                    alert('请选择文件');
                    return;
                }
                
                formData.append('file', fileInput.files[0]);
                if (customerName) formData.append('customer_name', customerName);
                formData.append('use_llm', useLlm);
                
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<p>处理中，请稍候...</p>';
                
                try {
                    const response = await fetch('/process-invoice', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        resultDiv.innerHTML = `
                            <h3 class="success">处理成功!</h3>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <h3 class="error">处理失败</h3>
                            <p class="error">${result.detail || '未知错误'}</p>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `
                        <h3 class="error">网络错误</h3>
                        <p class="error">${error.message}</p>
                    `;
                }
            });
        </script>
    </body>
    </html>
    """
    return html_content

@app.post("/process-invoice")
async def process_invoice(
    file: UploadFile = File(...),
    customer_name: Optional[str] = Form(None),
    use_llm: bool = Form(True)
):
    """处理上传的发票文件"""
    try:
        # 验证文件类型
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 获取文件扩展名
        file_extension = file.filename.split('.')[-1].lower()
        
        # 保存上传的文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_filename = f"{timestamp}_{file.filename}"
        file_path = os.path.join("uploads", saved_filename)
        
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        logger.info(f"文件已保存: {file_path}")
        
        # 步骤1: OCR文字提取
        logger.info("开始OCR文字提取...")
        ocr_result = ocr_service.process_file(file_content, file_extension)
        
        if not ocr_result["success"]:
            raise HTTPException(status_code=500, detail=f"OCR处理失败: {ocr_result.get('error', '未知错误')}")
        
        extracted_text = ocr_result["full_text"]
        logger.info(f"OCR提取文字: {extracted_text[:200]}...")
        
        # 步骤2: LLM结构化提取
        logger.info("开始LLM结构化提取...")
        llm_result = llm_service.extract_order_info(extracted_text)
        
        if not llm_result["success"]:
            raise HTTPException(status_code=500, detail=f"LLM处理失败: {llm_result.get('error', '未知错误')}")
        
        order_info = llm_result["data"]
        
        # 使用手动输入的客户姓名（如果提供）
        if customer_name:
            order_info["customer_name"] = customer_name
        
        # 如果没有日期，使用当前日期
        if not order_info.get("order_date"):
            order_info["order_date"] = datetime.now().strftime("%Y-%m-%d")
        
        logger.info(f"LLM提取信息: {order_info}")
        
        # 步骤3: 产品匹配和价格计算
        logger.info("开始产品匹配...")
        processing_result = product_service.process_order_items(
            order_info.get("items", []),
            use_llm_enhancement=use_llm
        )
        
        if not processing_result["success"]:
            raise HTTPException(status_code=500, detail=f"产品匹配失败: {processing_result.get('error', '未知错误')}")
        
        # 步骤4: 生成最终JSON
        final_order = product_service.generate_order_json(
            order_info.get("customer_name"),
            order_info.get("order_date"),
            processing_result["items"],
            processing_result["summary"]
        )
        
        # 添加处理信息
        final_order["processing_details"] = {
            "file_name": file.filename,
            "file_type": file_extension,
            "ocr_method": "EasyOCR",
            "llm_enhanced": use_llm,
            "processed_at": datetime.now().isoformat(),
            "extracted_text_preview": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text
        }
        
        # 验证订单
        validation_result = product_service.validate_order(final_order)
        final_order["validation"] = validation_result
        
        logger.info("订单处理完成")
        
        return final_order
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理发票时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

@app.get("/products")
async def get_products():
    """获取所有产品列表"""
    try:
        products = product_service.fuzzy_matcher.get_all_products()
        return {"products": products}
    except Exception as e:
        logger.error(f"获取产品列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/products/search")
async def search_products(q: str, limit: int = 5):
    """搜索产品"""
    try:
        suggestions = product_service.get_product_suggestions(q, limit)
        return {"suggestions": suggestions}
    except Exception as e:
        logger.error(f"搜索产品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/webhook/n8n")
async def n8n_webhook(request_data: dict):
    """n8n webhook接口"""
    try:
        # 这里处理来自n8n的请求
        # 通常n8n会发送文件URL或base64编码的文件数据
        
        file_url = request_data.get("file_url")
        file_data = request_data.get("file_data")  # base64编码
        customer_name = request_data.get("customer_name")
        use_llm = request_data.get("use_llm", True)
        
        if not file_url and not file_data:
            raise HTTPException(status_code=400, detail="缺少文件数据")
        
        # 处理文件数据
        if file_url:
            # 从URL下载文件
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get(file_url)
                file_content = response.content
                file_extension = file_url.split('.')[-1].lower()
        else:
            # 解码base64数据
            import base64
            file_content = base64.b64decode(file_data)
            file_extension = request_data.get("file_extension", "jpg")
        
        # 使用相同的处理逻辑
        # OCR提取
        ocr_result = ocr_service.process_file(file_content, file_extension)
        if not ocr_result["success"]:
            return {"error": f"OCR处理失败: {ocr_result.get('error')}"}
        
        # LLM提取
        llm_result = llm_service.extract_order_info(ocr_result["full_text"])
        if not llm_result["success"]:
            return {"error": f"LLM处理失败: {llm_result.get('error')}"}
        
        order_info = llm_result["data"]
        if customer_name:
            order_info["customer_name"] = customer_name
        
        # 产品匹配
        processing_result = product_service.process_order_items(
            order_info.get("items", []),
            use_llm_enhancement=use_llm
        )
        
        if not processing_result["success"]:
            return {"error": f"产品匹配失败: {processing_result.get('error')}"}
        
        # 生成最终结果
        final_order = product_service.generate_order_json(
            order_info.get("customer_name"),
            order_info.get("order_date"),
            processing_result["items"],
            processing_result["summary"]
        )
        
        return final_order
        
    except Exception as e:
        logger.error(f"n8n webhook处理失败: {e}")
        return {"error": str(e)}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
