#!/usr/bin/env python3
"""
Invoice Agent API 测试脚本
"""

import requests
import json
import os
import base64
from pathlib import Path

# API配置
API_BASE_URL = "http://localhost:8000"
N8N_WEBHOOK_URL = "http://localhost:5678/webhook/invoice-upload"

def test_health_check():
    """测试健康检查"""
    print("=== 测试健康检查 ===")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_get_products():
    """测试获取产品列表"""
    print("\n=== 测试获取产品列表 ===")
    try:
        response = requests.get(f"{API_BASE_URL}/products")
        print(f"状态码: {response.status_code}")
        data = response.json()
        print(f"产品数量: {len(data['products'])}")
        print("前3个产品:")
        for product in data['products'][:3]:
            print(f"  - {product['product_id']}: {product['name']} (¥{product['price']})")
        return response.status_code == 200
    except Exception as e:
        print(f"获取产品列表失败: {e}")
        return False

def test_search_products():
    """测试产品搜索"""
    print("\n=== 测试产品搜索 ===")
    try:
        search_terms = ["macbook", "显示器", "鼠标"]
        for term in search_terms:
            response = requests.get(f"{API_BASE_URL}/products/search", params={"q": term})
            print(f"搜索 '{term}' - 状态码: {response.status_code}")
            data = response.json()
            print(f"  找到 {len(data['suggestions'])} 个建议:")
            for suggestion in data['suggestions']:
                print(f"    - {suggestion['name']} (匹配度: {suggestion['match_score']:.2f})")
        return True
    except Exception as e:
        print(f"产品搜索失败: {e}")
        return False

def create_test_image():
    """创建测试图片（模拟手写订单）"""
    try:
        from PIL import Image, ImageDraw
        import io

        # 创建一个简单的测试图片
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)

        # 模拟手写订单内容
        text_lines = [
            "订单信息",
            "客户: 张三",
            "日期: 2025-01-15",
            "",
            "订购产品:",
            "1. MacBook Air M2 x 2",
            "2. Dell 24寸显示器 x 1",
            "3. 罗技鼠标 x 1",
            "",
            "总计: 请计算"
        ]

        y_position = 50
        for line in text_lines:
            draw.text((50, y_position), line, fill='black')
            y_position += 40

        # 保存为字节流
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes.seek(0)

        return img_bytes.getvalue()
    except ImportError:
        # 如果没有PIL，创建一个简单的文本文件
        text_content = """订单信息
客户: 张三
日期: 2025-01-15

订购产品:
1. MacBook Air M2 x 2
2. Dell 24寸显示器 x 1
3. 罗技鼠标 x 1

总计: 请计算"""
        return text_content.encode('utf-8')

def test_process_invoice_direct():
    """测试直接API调用处理发票"""
    print("\n=== 测试直接API处理发票 ===")
    try:
        # 创建测试图片
        test_image_data = create_test_image()

        # 准备文件上传
        files = {
            'file': ('test_order.png', test_image_data, 'image/png')
        }

        data = {
            'customer_name': '张三',
            'use_llm': 'true'
        }

        print("发送请求到API...")
        response = requests.post(f"{API_BASE_URL}/process-invoice", files=files, data=data)

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("处理成功!")
            print(f"客户: {result.get('customer_name')}")
            print(f"订单日期: {result.get('order_date')}")
            print(f"状态: {result.get('status')}")
            print(f"总金额: ¥{result.get('summary', {}).get('total_amount', 0)}")
            print(f"匹配项目: {result.get('summary', {}).get('matched_items', 0)}")

            print("\n订单项目:")
            for i, item in enumerate(result.get('items', []), 1):
                print(f"  {i}. {item.get('matched_name', 'N/A')} x {item.get('quantity', 0)}")
                print(f"     原始输入: {item.get('original_input')}")
                print(f"     匹配度: {item.get('match_score', 0):.2f}")
                print(f"     单价: ¥{item.get('unit_price', 0)}")
                print(f"     小计: ¥{item.get('total_price', 0)}")

            return True
        else:
            print(f"处理失败: {response.text}")
            return False

    except Exception as e:
        print(f"直接API测试失败: {e}")
        return False

def test_n8n_webhook():
    """测试n8n webhook"""
    print("\n=== 测试n8n Webhook ===")
    try:
        # 创建测试数据
        test_image_data = create_test_image()
        base64_data = base64.b64encode(test_image_data).decode('utf-8')

        payload = {
            "file_data": base64_data,
            "file_extension": "png",
            "customer_name": "李四",
            "use_llm": True
        }

        print("发送请求到n8n webhook...")
        response = requests.post(N8N_WEBHOOK_URL, json=payload)

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("n8n处理成功!")
            print(f"客户: {result.get('customer_name')}")
            print(f"状态: {result.get('status')}")
            print(f"成功: {result.get('success')}")
            return True
        else:
            print(f"n8n处理失败: {response.text}")
            return False

    except Exception as e:
        print(f"n8n webhook测试失败: {e}")
        return False

def test_text_extraction():
    """测试纯文本提取"""
    print("\n=== 测试文本提取 ===")

    # 模拟OCR提取的文本
    test_text = """
    订单信息
    客户姓名: 王五
    订单日期: 2025-01-15

    产品清单:
    1. 苹果笔记本 MacBook Air M2 数量: 1台
    2. 戴尔24寸显示器 数量: 2台
    3. 无线鼠标 罗技 数量: 1个

    备注: 请尽快发货
    """

    try:
        import sys
        sys.path.append('.')
        from services.llm_service import llm_service
        from services.product_service import product_service

        print("使用LLM提取结构化信息...")
        llm_result = llm_service.extract_order_info(test_text)

        if llm_result["success"]:
            order_info = llm_result["data"]
            print(f"提取成功!")
            print(f"客户: {order_info.get('customer_name')}")
            print(f"日期: {order_info.get('order_date')}")
            print(f"项目数: {len(order_info.get('items', []))}")

            # 产品匹配
            print("\n进行产品匹配...")
            processing_result = product_service.process_order_items(order_info.get("items", []))

            if processing_result["success"]:
                print("匹配成功!")
                print(f"匹配项目: {processing_result['summary']['matched_items']}")
                print(f"总金额: ¥{processing_result['summary']['total_amount']}")
                return True
            else:
                print(f"匹配失败: {processing_result.get('error')}")
                return False
        else:
            print(f"LLM提取失败: {llm_result.get('error')}")
            return False

    except Exception as e:
        print(f"文本提取测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始运行Invoice Agent测试套件...")
    print("=" * 50)

    tests = [
        ("健康检查", test_health_check),
        ("获取产品列表", test_get_products),
        ("产品搜索", test_search_products),
        ("文本提取", test_text_extraction),
        ("直接API处理", test_process_invoice_direct),
        ("n8n Webhook", test_n8n_webhook),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name} 测试出错: {e}")
            results.append((test_name, False))

    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)

    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{len(results)} 个测试通过")

    if passed == len(results):
        print("🎉 所有测试都通过了!")
    else:
        print("⚠️  有测试失败，请检查配置和服务状态")

if __name__ == "__main__":
    main()
