from typing import List, Dict, Any, Optional
import json
import logging
from .fuzzy_matching import fuzzy_matching_service
from .llm_service import llm_service

logger = logging.getLogger(__name__)

class ProductService:
    def __init__(self):
        """初始化产品服务"""
        self.fuzzy_matcher = fuzzy_matching_service
        self.llm_service = llm_service

    def process_order_items(self, items: List[Dict[str, Any]], 
                          use_llm_enhancement: bool = True,
                          match_threshold: float = 0.6) -> Dict[str, Any]:
        """处理订单项目，进行产品匹配和价格计算"""
        try:
            processed_items = []
            total_amount = 0.0
            low_confidence_items = []
            
            for item in items:
                product_name = item.get("product_name", "").strip()
                quantity = item.get("quantity", 1)
                original_text = item.get("original_text", product_name)
                
                if not product_name:
                    continue
                
                # 基础模糊匹配
                match_result = self.fuzzy_matcher.find_best_match(
                    product_name, threshold=match_threshold
                )
                
                # 如果匹配度较低且启用LLM增强，尝试使用LLM
                if (match_result["match_score"] < 0.8 and 
                    use_llm_enhancement and 
                    match_result["match_score"] > 0):
                    
                    available_products = [p["name"] for p in self.fuzzy_matcher.get_all_products()]
                    llm_result = self.llm_service.enhance_product_matching(
                        product_name, available_products
                    )
                    
                    if (llm_result["success"] and 
                        llm_result["confidence"] > match_result["match_score"]):
                        
                        # 使用LLM推荐的产品重新匹配
                        enhanced_match = self.fuzzy_matcher.find_best_match(
                            llm_result["match"], threshold=0.5
                        )
                        
                        if enhanced_match["match_score"] > match_result["match_score"]:
                            match_result = enhanced_match
                            match_result["llm_enhanced"] = True
                            match_result["llm_reasoning"] = llm_result["reasoning"]

                # 构建处理后的项目
                processed_item = {
                    "product_id": match_result["product_id"],
                    "matched_name": match_result["matched_name"],
                    "original_input": original_text,
                    "quantity": quantity,
                    "match_score": match_result["match_score"],
                    "match_method": match_result["match_method"],
                    "unit_price": 0.0,
                    "total_price": 0.0,
                    "status": "matched" if match_result["product_id"] else "unmatched"
                }
                
                # 添加LLM增强信息
                if "llm_enhanced" in match_result:
                    processed_item["llm_enhanced"] = match_result["llm_enhanced"]
                    processed_item["llm_reasoning"] = match_result.get("llm_reasoning", "")
                
                # 计算价格
                if match_result["product_info"]:
                    unit_price = match_result["product_info"]["price"]
                    total_price = unit_price * quantity
                    
                    processed_item["unit_price"] = unit_price
                    processed_item["total_price"] = total_price
                    total_amount += total_price
                
                # 检查是否需要人工确认
                if match_result["match_score"] < 0.7:
                    processed_item["requires_confirmation"] = True
                    low_confidence_items.append(processed_item)
                
                processed_items.append(processed_item)
            
            # 计算统计信息
            matched_count = sum(1 for item in processed_items if item["status"] == "matched")
            unmatched_count = len(processed_items) - matched_count
            
            return {
                "success": True,
                "items": processed_items,
                "summary": {
                    "total_items": len(processed_items),
                    "matched_items": matched_count,
                    "unmatched_items": unmatched_count,
                    "total_amount": total_amount,
                    "low_confidence_items": len(low_confidence_items)
                },
                "low_confidence_items": low_confidence_items
            }
            
        except Exception as e:
            logger.error(f"处理订单项目失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "items": [],
                "summary": {},
                "low_confidence_items": []
            }

    def generate_order_json(self, customer_name: Optional[str],
                           order_date: Optional[str],
                           processed_items: List[Dict[str, Any]],
                           summary: Dict[str, Any]) -> Dict[str, Any]:
        """生成标准化的订单JSON"""
        try:
            # 确定订单状态
            status = "completed"
            if summary.get("unmatched_items", 0) > 0:
                status = "pending"
            elif summary.get("low_confidence_items", 0) > 0:
                status = "needs_review"
            
            # 构建订单JSON
            order_json = {
                "customer_name": customer_name,
                "order_date": order_date,
                "items": [],
                "summary": {
                    "total_items": summary.get("total_items", 0),
                    "matched_items": summary.get("matched_items", 0),
                    "unmatched_items": summary.get("unmatched_items", 0),
                    "total_amount": summary.get("total_amount", 0.0),
                    "currency": "CNY"
                },
                "status": status,
                "processing_info": {
                    "low_confidence_items": summary.get("low_confidence_items", 0),
                    "requires_manual_review": status in ["pending", "needs_review"]
                }
            }
            
            # 添加项目信息
            for item in processed_items:
                order_item = {
                    "product_id": item["product_id"],
                    "matched_name": item["matched_name"],
                    "original_input": item["original_input"],
                    "quantity": item["quantity"],
                    "match_score": round(item["match_score"], 3),
                    "unit_price": item["unit_price"],
                    "total_price": item["total_price"]
                }
                
                # 添加可选字段
                if item.get("requires_confirmation"):
                    order_item["requires_confirmation"] = True
                
                if item.get("llm_enhanced"):
                    order_item["llm_enhanced"] = True
                    order_item["llm_reasoning"] = item.get("llm_reasoning", "")
                
                order_json["items"].append(order_item)
            
            return order_json
            
        except Exception as e:
            logger.error(f"生成订单JSON失败: {e}")
            return {
                "error": str(e),
                "status": "error"
            }

    def get_product_suggestions(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """获取产品建议"""
        try:
            all_products = self.fuzzy_matcher.get_all_products()
            suggestions = []
            
            for product in all_products:
                match_result = self.fuzzy_matcher.find_best_match(query, threshold=0.3)
                if match_result["product_id"] == product["product_id"]:
                    suggestions.append({
                        "product_id": product["product_id"],
                        "name": product["name"],
                        "price": product["price"],
                        "match_score": match_result["match_score"]
                    })
            
            # 按匹配度排序
            suggestions.sort(key=lambda x: x["match_score"], reverse=True)
            
            return suggestions[:limit]
            
        except Exception as e:
            logger.error(f"获取产品建议失败: {e}")
            return []

    def validate_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证订单数据"""
        try:
            validation_result = {
                "valid": True,
                "errors": [],
                "warnings": []
            }
            
            # 检查必需字段
            if not order_data.get("customer_name"):
                validation_result["warnings"].append("缺少客户姓名")
            
            if not order_data.get("order_date"):
                validation_result["warnings"].append("缺少订单日期")
            
            # 检查项目
            items = order_data.get("items", [])
            if not items:
                validation_result["valid"] = False
                validation_result["errors"].append("订单中没有项目")
            
            for i, item in enumerate(items):
                if not item.get("product_id"):
                    validation_result["errors"].append(f"项目 {i+1} 缺少产品ID")
                
                if item.get("quantity", 0) <= 0:
                    validation_result["errors"].append(f"项目 {i+1} 数量无效")
                
                if item.get("match_score", 0) < 0.5:
                    validation_result["warnings"].append(f"项目 {i+1} 匹配度较低")
            
            if validation_result["errors"]:
                validation_result["valid"] = False
            
            return validation_result
            
        except Exception as e:
            logger.error(f"订单验证失败: {e}")
            return {
                "valid": False,
                "errors": [f"验证过程出错: {e}"],
                "warnings": []
            }

# 全局产品服务实例
product_service = ProductService()
