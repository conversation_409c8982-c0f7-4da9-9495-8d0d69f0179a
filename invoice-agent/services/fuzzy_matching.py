from fuzzywuzzy import fuzz, process
from typing import List, Dict, Any, Tuple, Optional
import json
import logging
import re
import os

logger = logging.getLogger(__name__)

class FuzzyMatchingService:
    def __init__(self, products_file: str = "data/products.json"):
        """初始化模糊匹配服务"""
        self.products_file = products_file
        self.products = []
        self.product_names = []
        self.alias_to_product = {}
        self.load_products()

    def load_products(self):
        """加载产品数据"""
        try:
            products_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), self.products_file)
            with open(products_path, 'r', encoding='utf-8') as f:
                self.products = json.load(f)
            
            # 构建产品名称列表和别名映射
            self.product_names = []
            self.alias_to_product = {}
            
            for product in self.products:
                product_name = product['name']
                self.product_names.append(product_name)
                
                # 添加主名称到映射
                self.alias_to_product[product_name.lower()] = product
                
                # 添加别名到映射
                for alias in product.get('aliases', []):
                    self.alias_to_product[alias.lower()] = product
            
            logger.info(f"成功加载 {len(self.products)} 个产品")
            
        except Exception as e:
            logger.error(f"加载产品数据失败: {e}")
            self.products = []
            self.product_names = []
            self.alias_to_product = {}

    def normalize_text(self, text: str) -> str:
        """标准化文本"""
        if not text:
            return ""
        
        # 转换为小写
        text = text.lower().strip()
        
        # 移除多余的空格
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留中文、英文、数字、空格）
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)
        
        return text

    def find_best_match(self, query: str, threshold: float = 0.6) -> Dict[str, Any]:
        """找到最佳匹配的产品"""
        if not query or not self.products:
            return {
                "product_id": None,
                "matched_name": None,
                "original_input": query,
                "match_score": 0.0,
                "match_method": "no_match",
                "product_info": None
            }

        normalized_query = self.normalize_text(query)
        
        # 方法1: 精确匹配（包括别名）
        exact_match = self._exact_match(normalized_query)
        if exact_match:
            return exact_match

        # 方法2: 模糊匹配产品名称
        fuzzy_match = self._fuzzy_match_names(normalized_query, threshold)
        if fuzzy_match and fuzzy_match["match_score"] >= threshold:
            return fuzzy_match

        # 方法3: 模糊匹配别名
        alias_match = self._fuzzy_match_aliases(normalized_query, threshold)
        if alias_match and alias_match["match_score"] >= threshold:
            return alias_match

        # 方法4: 部分匹配
        partial_match = self._partial_match(normalized_query, threshold * 0.8)
        if partial_match:
            return partial_match

        # 没有找到匹配
        return {
            "product_id": None,
            "matched_name": None,
            "original_input": query,
            "match_score": 0.0,
            "match_method": "no_match",
            "product_info": None
        }

    def _exact_match(self, query: str) -> Optional[Dict[str, Any]]:
        """精确匹配"""
        if query in self.alias_to_product:
            product = self.alias_to_product[query]
            return {
                "product_id": product["product_id"],
                "matched_name": product["name"],
                "original_input": query,
                "match_score": 1.0,
                "match_method": "exact_match",
                "product_info": product
            }
        return None

    def _fuzzy_match_names(self, query: str, threshold: float) -> Optional[Dict[str, Any]]:
        """模糊匹配产品名称"""
        if not self.product_names:
            return None

        # 使用不同的匹配算法
        ratios = []
        for name in self.product_names:
            normalized_name = self.normalize_text(name)
            
            # 计算多种相似度
            ratio = fuzz.ratio(query, normalized_name)
            partial_ratio = fuzz.partial_ratio(query, normalized_name)
            token_sort_ratio = fuzz.token_sort_ratio(query, normalized_name)
            token_set_ratio = fuzz.token_set_ratio(query, normalized_name)
            
            # 综合评分
            combined_score = max(ratio, partial_ratio, token_sort_ratio, token_set_ratio) / 100.0
            ratios.append((name, combined_score))

        # 找到最佳匹配
        best_match = max(ratios, key=lambda x: x[1])
        
        if best_match[1] >= threshold:
            # 找到对应的产品
            product = next(p for p in self.products if p["name"] == best_match[0])
            return {
                "product_id": product["product_id"],
                "matched_name": product["name"],
                "original_input": query,
                "match_score": best_match[1],
                "match_method": "fuzzy_match_name",
                "product_info": product
            }
        
        return None

    def _fuzzy_match_aliases(self, query: str, threshold: float) -> Optional[Dict[str, Any]]:
        """模糊匹配别名"""
        best_score = 0
        best_product = None
        
        for alias, product in self.alias_to_product.items():
            # 计算相似度
            ratio = fuzz.ratio(query, alias)
            partial_ratio = fuzz.partial_ratio(query, alias)
            token_sort_ratio = fuzz.token_sort_ratio(query, alias)
            
            combined_score = max(ratio, partial_ratio, token_sort_ratio) / 100.0
            
            if combined_score > best_score:
                best_score = combined_score
                best_product = product

        if best_score >= threshold and best_product:
            return {
                "product_id": best_product["product_id"],
                "matched_name": best_product["name"],
                "original_input": query,
                "match_score": best_score,
                "match_method": "fuzzy_match_alias",
                "product_info": best_product
            }
        
        return None

    def _partial_match(self, query: str, threshold: float) -> Optional[Dict[str, Any]]:
        """部分匹配（关键词匹配）"""
        query_words = set(query.split())
        best_score = 0
        best_product = None
        
        for product in self.products:
            # 检查产品名称和别名中的关键词
            all_names = [product["name"]] + product.get("aliases", [])
            
            for name in all_names:
                normalized_name = self.normalize_text(name)
                name_words = set(normalized_name.split())
                
                # 计算词汇重叠度
                if query_words and name_words:
                    intersection = query_words.intersection(name_words)
                    union = query_words.union(name_words)
                    jaccard_score = len(intersection) / len(union) if union else 0
                    
                    # 考虑查询词在产品名称中的覆盖度
                    query_coverage = len(intersection) / len(query_words) if query_words else 0
                    
                    # 综合评分
                    combined_score = (jaccard_score + query_coverage) / 2
                    
                    if combined_score > best_score:
                        best_score = combined_score
                        best_product = product

        if best_score >= threshold and best_product:
            return {
                "product_id": best_product["product_id"],
                "matched_name": best_product["name"],
                "original_input": query,
                "match_score": best_score,
                "match_method": "partial_match",
                "product_info": best_product
            }
        
        return None

    def batch_match(self, queries: List[str], threshold: float = 0.6) -> List[Dict[str, Any]]:
        """批量匹配"""
        results = []
        for query in queries:
            result = self.find_best_match(query, threshold)
            results.append(result)
        return results

    def get_all_products(self) -> List[Dict[str, Any]]:
        """获取所有产品"""
        return self.products

    def get_product_by_id(self, product_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取产品"""
        for product in self.products:
            if product["product_id"] == product_id:
                return product
        return None

    def add_product(self, product: Dict[str, Any]) -> bool:
        """添加新产品"""
        try:
            # 验证产品数据
            required_fields = ["product_id", "name", "price"]
            for field in required_fields:
                if field not in product:
                    logger.error(f"产品缺少必需字段: {field}")
                    return False
            
            # 检查产品ID是否已存在
            if any(p["product_id"] == product["product_id"] for p in self.products):
                logger.error(f"产品ID已存在: {product['product_id']}")
                return False
            
            # 添加产品
            self.products.append(product)
            
            # 重新加载数据结构
            self.load_products()
            
            logger.info(f"成功添加产品: {product['product_id']}")
            return True
            
        except Exception as e:
            logger.error(f"添加产品失败: {e}")
            return False

# 全局模糊匹配服务实例
fuzzy_matching_service = FuzzyMatchingService()
