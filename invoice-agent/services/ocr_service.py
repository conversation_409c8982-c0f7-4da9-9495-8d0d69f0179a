import easyocr
import cv2
import numpy as np
from PIL import Image
import io
import logging
from typing import List, Dict, Any
import os

logger = logging.getLogger(__name__)

class OCRService:
    def __init__(self):
        """初始化OCR服务，支持中英文识别"""
        try:
            # 初始化EasyOCR，支持中文和英文
            self.reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            logger.info("OCR服务初始化成功")
        except Exception as e:
            logger.error(f"OCR服务初始化失败: {e}")
            raise

    def preprocess_image(self, image_data: bytes) -> np.ndarray:
        """预处理图像以提高OCR准确率"""
        try:
            # 将字节数据转换为PIL图像
            image = Image.open(io.BytesIO(image_data))
            
            # 转换为RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 转换为numpy数组
            img_array = np.array(image)
            
            # 转换为灰度图
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # 应用高斯模糊去噪
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # 自适应阈值处理
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 形态学操作去除噪点
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            # 如果预处理失败，返回原始图像
            image = Image.open(io.BytesIO(image_data))
            return np.array(image)

    def extract_text_from_image(self, image_data: bytes) -> Dict[str, Any]:
        """从图像中提取文字"""
        try:
            # 预处理图像
            processed_image = self.preprocess_image(image_data)
            
            # 使用EasyOCR进行文字识别
            results = self.reader.readtext(processed_image)
            
            # 整理识别结果
            extracted_text = []
            full_text = ""
            
            for (bbox, text, confidence) in results:
                if confidence > 0.3:  # 只保留置信度较高的结果
                    extracted_text.append({
                        "text": text.strip(),
                        "confidence": float(confidence),
                        "bbox": bbox
                    })
                    full_text += text.strip() + " "
            
            return {
                "success": True,
                "full_text": full_text.strip(),
                "detailed_results": extracted_text,
                "total_items": len(extracted_text)
            }
            
        except Exception as e:
            logger.error(f"OCR文字提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "full_text": "",
                "detailed_results": [],
                "total_items": 0
            }

    def extract_text_from_pdf(self, pdf_data: bytes) -> Dict[str, Any]:
        """从PDF中提取文字（简单实现）"""
        try:
            import pypdf
            
            # 读取PDF
            pdf_reader = pypdf.PdfReader(io.BytesIO(pdf_data))
            
            full_text = ""
            page_texts = []
            
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        page_texts.append({
                            "page": page_num + 1,
                            "text": page_text.strip()
                        })
                        full_text += page_text.strip() + "\n"
                except Exception as e:
                    logger.warning(f"PDF第{page_num + 1}页文字提取失败: {e}")
            
            return {
                "success": True,
                "full_text": full_text.strip(),
                "page_texts": page_texts,
                "total_pages": len(pdf_reader.pages)
            }
            
        except Exception as e:
            logger.error(f"PDF文字提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "full_text": "",
                "page_texts": [],
                "total_pages": 0
            }

    def process_file(self, file_data: bytes, file_type: str) -> Dict[str, Any]:
        """根据文件类型处理文件"""
        try:
            if file_type.lower() in ['jpg', 'jpeg', 'png', 'bmp', 'tiff']:
                return self.extract_text_from_image(file_data)
            elif file_type.lower() == 'pdf':
                return self.extract_text_from_pdf(file_data)
            else:
                return {
                    "success": False,
                    "error": f"不支持的文件类型: {file_type}",
                    "full_text": "",
                    "detailed_results": [],
                    "total_items": 0
                }
        except Exception as e:
            logger.error(f"文件处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "full_text": "",
                "detailed_results": [],
                "total_items": 0
            }

# 全局OCR服务实例
ocr_service = OCRService()
