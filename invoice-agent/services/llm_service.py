import openai
import json
import logging
from typing import Dict, List, Any, Optional
import os
from dotenv import load_dotenv
import re

load_dotenv()
logger = logging.getLogger(__name__)

class LLMService:
    def __init__(self):
        """初始化LLM服务"""
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY环境变量未设置")
        
        # 设置代理
        http_proxy = os.getenv("HTTP_PROXY")
        https_proxy = os.getenv("HTTPS_PROXY")
        
        if http_proxy or https_proxy:
            import httpx
            proxies = {}
            if http_proxy:
                proxies["http://"] = http_proxy
            if https_proxy:
                proxies["https://"] = https_proxy
            
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.base_url,
                http_client=httpx.Client(proxies=proxies)
            )
        else:
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
        
        logger.info("LLM服务初始化成功")

    def extract_order_info(self, text: str) -> Dict[str, Any]:
        """使用LLM从文本中提取订单信息"""
        try:
            system_prompt = """你是一个专业的订单信息提取助手。请从给定的文本中提取以下信息：

1. 客户姓名 (customer_name)
2. 订单日期 (order_date) - 格式：YYYY-MM-DD
3. 订购的产品列表 (items)，每个产品包含：
   - 产品名称 (product_name)
   - 数量 (quantity)
   - 原始输入文本 (original_text)

请以JSON格式返回结果，格式如下：
{
  "customer_name": "客户姓名",
  "order_date": "2025-01-XX",
  "items": [
    {
      "product_name": "产品名称",
      "quantity": 数量,
      "original_text": "原始文本"
    }
  ]
}

注意事项：
- 如果找不到某些信息，请设置为null
- 数量必须是数字，如果文本中是中文数字请转换为阿拉伯数字
- 产品名称请尽量标准化，但保留原始输入在original_text中
- 支持中英文混合识别"""

            user_prompt = f"请从以下文本中提取订单信息：\n\n{text}"

            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )

            # 提取响应内容
            content = response.choices[0].message.content.strip()
            
            # 尝试解析JSON
            try:
                # 查找JSON内容
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    result = json.loads(json_str)
                else:
                    result = json.loads(content)
                
                # 验证和清理结果
                cleaned_result = self._clean_extraction_result(result)
                
                return {
                    "success": True,
                    "data": cleaned_result,
                    "raw_response": content
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}, 原始响应: {content}")
                return {
                    "success": False,
                    "error": f"JSON解析失败: {e}",
                    "raw_response": content
                }

        except Exception as e:
            logger.error(f"LLM提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "raw_response": ""
            }

    def _clean_extraction_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """清理和验证提取结果"""
        cleaned = {
            "customer_name": result.get("customer_name"),
            "order_date": result.get("order_date"),
            "items": []
        }
        
        # 处理items
        items = result.get("items", [])
        if isinstance(items, list):
            for item in items:
                if isinstance(item, dict):
                    cleaned_item = {
                        "product_name": item.get("product_name", "").strip(),
                        "quantity": self._parse_quantity(item.get("quantity")),
                        "original_text": item.get("original_text", "").strip()
                    }
                    
                    # 只添加有效的项目
                    if cleaned_item["product_name"] and cleaned_item["quantity"] > 0:
                        cleaned["items"].append(cleaned_item)
        
        return cleaned

    def _parse_quantity(self, quantity) -> int:
        """解析数量，支持中文数字"""
        if isinstance(quantity, int):
            return max(0, quantity)
        
        if isinstance(quantity, str):
            # 中文数字映射
            chinese_numbers = {
                '零': 0, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
                '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
                '两': 2, '俩': 2
            }
            
            quantity = quantity.strip()
            
            # 尝试直接转换为数字
            try:
                return max(0, int(quantity))
            except ValueError:
                pass
            
            # 尝试中文数字转换
            if quantity in chinese_numbers:
                return chinese_numbers[quantity]
            
            # 提取数字
            numbers = re.findall(r'\d+', quantity)
            if numbers:
                return max(0, int(numbers[0]))
        
        return 1  # 默认数量为1

    def enhance_product_matching(self, product_name: str, available_products: List[str]) -> Dict[str, Any]:
        """使用LLM增强产品匹配"""
        try:
            system_prompt = """你是一个产品匹配专家。给定一个产品名称和可用产品列表，请找出最匹配的产品。

请考虑以下因素：
1. 产品名称的相似性
2. 品牌匹配
3. 产品类型匹配
4. 常见的缩写和别名
5. 中英文对应关系

请返回JSON格式：
{
  "best_match": "最匹配的产品名称",
  "confidence": 0.95,
  "reasoning": "匹配理由"
}

如果没有合适的匹配，请返回confidence为0。"""

            user_prompt = f"""
产品名称: {product_name}

可用产品列表:
{chr(10).join(f"- {p}" for p in available_products)}

请找出最匹配的产品。"""

            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            content = response.choices[0].message.content.strip()
            
            # 解析JSON响应
            try:
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                else:
                    result = json.loads(content)
                
                return {
                    "success": True,
                    "match": result.get("best_match"),
                    "confidence": float(result.get("confidence", 0)),
                    "reasoning": result.get("reasoning", "")
                }
                
            except json.JSONDecodeError:
                return {
                    "success": False,
                    "match": None,
                    "confidence": 0,
                    "reasoning": "JSON解析失败"
                }

        except Exception as e:
            logger.error(f"LLM产品匹配失败: {e}")
            return {
                "success": False,
                "match": None,
                "confidence": 0,
                "reasoning": f"错误: {e}"
            }

# 全局LLM服务实例
llm_service = LLMService()
