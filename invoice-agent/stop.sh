#!/bin/bash

# Invoice Agent 停止脚本

echo "🛑 停止 Invoice Agent 系统..."

# 停止API服务
if [ -f ".api_pid" ]; then
    API_PID=$(cat .api_pid)
    if ps -p $API_PID > /dev/null; then
        echo "🔄 停止API服务 (PID: $API_PID)..."
        kill $API_PID
        echo "✅ API服务已停止"
    else
        echo "ℹ️  API服务未运行"
    fi
    rm -f .api_pid
else
    echo "ℹ️  未找到API服务PID文件"
fi

# 停止n8n服务
if [ -f "n8n/docker-compose.yml" ]; then
    echo "🔄 停止n8n服务..."
    cd n8n
    docker-compose down
    cd ..
    echo "✅ n8n服务已停止"
fi

# 清理临时文件
echo "🧹 清理临时文件..."
find uploads -name "*.tmp" -delete 2>/dev/null || true

echo "✅ Invoice Agent 系统已完全停止"
