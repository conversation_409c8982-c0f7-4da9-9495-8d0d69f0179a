# Invoice Agent - 智能订单处理系统

基于 n8n 工作流和 AI 技术的智能订单处理系统，能够从非结构化文档（PDF、图片、手写等）中自动提取订单信息，进行产品匹配和价格计算。

## 🌟 主要功能

- **多格式文件支持**: 支持 PDF、JPG、PNG 等格式的订单文档
- **OCR 文字识别**: 使用 EasyOCR 进行中英文文字识别
- **AI 结构化提取**: 使用 GPT-4 从文本中提取结构化订单信息
- **智能产品匹配**: 模糊匹配算法 + LLM 增强，处理拼写错误、缩写等
- **n8n 工作流**: 完整的自动化处理流程
- **人工审核机制**: 低置信度订单自动标记需要人工确认

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   文件上传      │    │   n8n 工作流    │    │   Invoice API   │
│  (表单/Webhook) │───▶│   自动化处理    │───▶│   核心处理逻辑   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   产品数据库    │◀───│   处理服务群    │
                       │   模糊匹配      │    │ OCR+LLM+匹配   │
                       └─────────────────┘    └─────────────────┘
```

## 📋 技术栈

- **工作流引擎**: n8n
- **Web 框架**: FastAPI
- **OCR 引擎**: EasyOCR (支持中英文)
- **AI 模型**: OpenAI GPT-4
- **模糊匹配**: FuzzyWuzzy + Levenshtein
- **容器化**: Docker & Docker Compose

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd invoice-agent

# 确保已安装 Python 3.8+ 和 Docker
python3 --version
docker --version
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env
```

必需配置：
```env
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
HTTP_PROXY=http://localhost:1080  # 如需代理
HTTPS_PROXY=http://localhost:1080
```

### 3. 启动系统

```bash
# 给脚本执行权限
chmod +x start.sh stop.sh

# 启动所有服务
./start.sh
```

启动后可访问：
- **Web 界面**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **n8n 工作流**: http://localhost:5678 (admin/admin123)

### 4. 测试系统

```bash
# 运行测试套件
python test_api.py
```

## 📖 使用说明

### Web 界面上传

1. 访问 http://localhost:8000
2. 选择订单文件（支持图片或PDF）
3. 可选填写客户姓名
4. 选择是否启用LLM增强匹配
5. 点击"处理订单"

### API 调用

```python
import requests

# 直接API调用
files = {'file': open('order.jpg', 'rb')}
data = {'customer_name': '张三', 'use_llm': 'true'}
response = requests.post('http://localhost:8000/process-invoice', 
                        files=files, data=data)
result = response.json()
```

### n8n Webhook

```bash
# 发送到n8n webhook
curl -X POST http://localhost:5678/webhook/invoice-upload \
  -H "Content-Type: application/json" \
  -d '{
    "file_data": "base64_encoded_file_data",
    "file_extension": "jpg",
    "customer_name": "张三",
    "use_llm": true
  }'
```

## 📊 输出格式

系统输出标准化的JSON格式：

```json
{
  "customer_name": "张三",
  "order_date": "2025-01-15",
  "items": [
    {
      "product_id": "P001",
      "matched_name": "Apple MacBook Air M2",
      "original_input": "mac air laptop",
      "quantity": 2,
      "match_score": 0.92,
      "unit_price": 8999.00,
      "total_price": 17998.00
    }
  ],
  "summary": {
    "total_items": 1,
    "matched_items": 1,
    "unmatched_items": 0,
    "total_amount": 17998.00,
    "currency": "CNY"
  },
  "status": "completed",
  "processing_info": {
    "requires_manual_review": false
  }
}
```

## 🔧 配置说明

### 产品数据

产品信息存储在 `data/products.json`：

```json
{
  "product_id": "P001",
  "name": "Apple MacBook Air M2",
  "aliases": ["MacBook Air", "mac air", "苹果笔记本"],
  "price": 8999.00,
  "category": "laptop"
}
```

### 匹配阈值

- **精确匹配**: 1.0 (完全匹配)
- **模糊匹配**: 0.6+ (可调整)
- **需要审核**: <0.7
- **LLM增强**: 当模糊匹配 <0.8 时触发

## 🔄 n8n 工作流

工作流包含以下节点：

1. **Webhook Trigger**: 接收文件上传
2. **File Processing**: 处理文件数据
3. **API Call**: 调用处理API
4. **Result Check**: 检查处理结果
5. **Notification**: 发送审核通知（可选）
6. **Response**: 返回处理结果

### 导入工作流

1. 访问 n8n 界面 (http://localhost:5678)
2. 点击 "Import from file"
3. 选择 `n8n/workflows/invoice-processing-workflow.json`

## 🧪 测试用例

### 测试图片

系统包含测试脚本，会自动生成包含以下内容的测试图片：

```
订单信息
客户: 张三
日期: 2025-01-15

订购产品:
1. MacBook Air M2 x 2
2. Dell 24寸显示器 x 1
3. 罗技鼠标 x 1
```

### 边界情况

- 空白文档
- 无法识别的产品
- 模糊的手写文字
- 中英文混合内容

## 🔍 故障排除

### 常见问题

1. **OCR识别失败**
   - 检查图片质量和清晰度
   - 确保EasyOCR正确安装

2. **LLM调用失败**
   - 验证OPENAI_API_KEY设置
   - 检查网络连接和代理配置

3. **产品匹配度低**
   - 检查产品数据库内容
   - 调整匹配阈值
   - 启用LLM增强匹配

4. **n8n连接失败**
   - 确保Docker服务运行
   - 检查端口占用情况

### 日志查看

```bash
# API服务日志
tail -f logs/api.log

# n8n容器日志
docker-compose -f n8n/docker-compose.yml logs -f
```

## 🛠️ 开发指南

### 项目结构

```
invoice-agent/
├── api/                 # FastAPI应用
├── services/           # 核心服务模块
│   ├── ocr_service.py     # OCR文字识别
│   ├── llm_service.py     # LLM结构化提取
│   ├── fuzzy_matching.py  # 模糊匹配
│   └── product_service.py # 产品服务
├── n8n/                # n8n配置
├── data/               # 数据文件
├── uploads/            # 上传文件目录
└── tests/              # 测试文件
```

### 添加新产品

编辑 `data/products.json`：

```json
{
  "product_id": "P011",
  "name": "新产品名称",
  "aliases": ["别名1", "别名2"],
  "price": 1999.00,
  "category": "分类"
}
```

### 自定义匹配算法

修改 `services/fuzzy_matching.py` 中的匹配逻辑。

## 📈 性能优化

- **OCR缓存**: 相同文件避免重复OCR
- **LLM调用优化**: 批量处理和缓存
- **数据库索引**: 产品匹配性能优化
- **异步处理**: 大文件异步处理

## 🔒 安全考虑

- API访问控制
- 文件类型验证
- 上传文件大小限制
- 敏感信息脱敏

## 📝 更新日志

### v1.0.0 (2025-01-15)
- 初始版本发布
- 支持基础OCR和LLM处理
- n8n工作流集成
- 模糊匹配算法

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交 Issue 或联系开发团队。
